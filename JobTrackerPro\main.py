import os
import sys

# Add the api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

# Import the robust app creation function from api/index.py
from api.index import create_app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Starting JobTrackerPro server on http://localhost:5000")
    print("📋 API endpoints available at http://localhost:5000/api/")
    print("🌐 Frontend available at http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
