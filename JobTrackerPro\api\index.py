import os
import sys
import io
import traceback
from flask import Flask, jsonify
from flask_cors import CORS

# Add the current directory to the Python path so we can import local modules
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import local modules with error handling
try:
    from database import db, get_database_url
except ImportError as e:
    print(f"❌ Failed to import database: {e}")
    # Create a minimal db object for fallback
    class MockDB:
        def init_app(self, app): pass
        def create_all(self): pass
        class session:
            @staticmethod
            def execute(query): raise Exception("Database not available")
    db = MockDB()
    def get_database_url(): return 'sqlite:///:memory:'

def create_app():
    app = Flask(__name__)

    # Enable CORS for all routes
    CORS(app)

    # Setup secret key from environment
    app.secret_key = os.environ.get("FLASK_SECRET_KEY") or "job-tracker-secret-key"

    # Configure database using helper function
    try:
        database_url = get_database_url()
        print(f"🔗 Using database: {database_url[:50]}...")
    except Exception as e:
        print(f"⚠️ Database URL error: {e}")
        database_url = "sqlite:///:memory:"
        print("⚠️ Falling back to in-memory SQLite database")

    app.config["SQLALCHEMY_DATABASE_URI"] = database_url
    app.config["SQLALCHEMY_ENGINE_OPTIONS"] = {
        "pool_recycle": 300,
        "pool_pre_ping": True,
    }
    app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

    # Initialize extensions
    db.init_app(app)

    # Add a health check endpoint first
    @app.route('/health')
    def health():
        try:
            # Test database connection
            db.session.execute(db.text('SELECT 1'))
            db_status = 'connected'
        except Exception as e:
            db_status = f'disconnected: {str(e)}'

        return jsonify({
            'status': 'ok',
            'message': 'API is running',
            'database': db_status,
            'database_url': app.config.get('SQLALCHEMY_DATABASE_URI', 'not set')[:50] + '...'
        })

    # Add a debug endpoint to check imports
    @app.route('/debug')
    def debug():
        debug_info = {
            'status': 'ok',
            'python_path': sys.path[:5],
            'current_dir': current_dir,
            'environment': dict(os.environ),
            'imports': {}
        }

        # Test database import
        try:
            import database
            debug_info['imports']['database'] = 'success'
        except Exception as e:
            debug_info['imports']['database'] = f'failed: {str(e)}'

        # Test models import
        try:
            import models
            debug_info['imports']['models'] = 'success'
        except Exception as e:
            debug_info['imports']['models'] = f'failed: {str(e)}'

        # Test app import
        try:
            import app
            debug_info['imports']['app'] = 'success'
        except Exception as e:
            debug_info['imports']['app'] = f'failed: {str(e)}'

        return jsonify(debug_info)

    # Initialize database with error handling
    with app.app_context():
        try:
            # Import models to ensure tables are created
            import models
            db.create_all()
            print("✅ Database tables created successfully")
        except Exception as e:
            print(f"⚠️ Database initialization warning: {e}")
            # Continue without database - will use sample data

    # Register blueprints with comprehensive error handling
    blueprint_registered = False
    try:
        # Import the blueprint from the local app.py file
        from app import api_bp
        app.register_blueprint(api_bp)
        blueprint_registered = True
        print("✅ API blueprint registered successfully")

    except Exception as e:
        print(f"❌ Blueprint registration error: {e}")
        print(f"❌ Error details: {traceback.format_exc()}")

    # Add fallback endpoints if blueprint registration failed
    if not blueprint_registered:
        @app.route('/jobs', methods=['GET'])
        def fallback_jobs():
            return jsonify([{
                'id': 1,
                'company': 'Sample Company',
                'position': 'Software Engineer',
                'location': 'Remote',
                'status': 'Applied',
                'application_date': '2025-05-31T00:00:00',
                'last_updated': '2025-05-31T00:00:00',
                'notes': 'Fallback sample data - blueprint registration failed',
                'tags': []
            }])

        @app.route('/statistics', methods=['GET'])
        def fallback_statistics():
            return jsonify({
                'total_applications': 1,
                'status_counts': {'Applied': 1, 'Interviewing': 0, 'Offer': 0, 'Rejected': 0, 'Withdrawn': 0},
                'applications_this_month': 1
            })

        @app.route('/tags', methods=['GET'])
        def fallback_tags():
            return jsonify([])

    # Add test endpoint
    @app.route('/test')
    def test_endpoint():
        return jsonify({
            'status': 'success',
            'message': 'API is working correctly',
            'blueprint_registered': blueprint_registered,
            'endpoints': [str(rule) for rule in app.url_map.iter_rules() if not rule.rule.startswith('/static')]
        })

    # Add global error handler
    @app.errorhandler(500)
    def handle_500(error):
        return jsonify({
            'status': 'error',
            'message': 'Internal server error',
            'error': str(error),
            'traceback': traceback.format_exc()
        }), 500

    @app.errorhandler(404)
    def handle_404(error):
        return jsonify({
            'status': 'error',
            'message': 'Endpoint not found',
            'available_endpoints': [str(rule) for rule in app.url_map.iter_rules() if not rule.rule.startswith('/static')]
        }), 404

    return app

# Create the app instance for Vercel
try:
    app = create_app()
    print("✅ Flask app created successfully")
except Exception as e:
    print(f"❌ Failed to create Flask app: {e}")
    print(f"❌ Error details: {traceback.format_exc()}")
    # Create a minimal fallback app
    app = Flask(__name__)
    CORS(app)

    @app.route('/')
    def fallback_root():
        return jsonify({
            'status': 'error',
            'message': 'App initialization failed',
            'error': str(e)
        })

    @app.route('/health')
    def fallback_health():
        return jsonify({
            'status': 'error',
            'message': 'App initialization failed',
            'error': str(e)
        })

# Add a simple root endpoint for testing
@app.route('/')
def root():
    return jsonify({
        'status': 'ok',
        'message': 'JobTracker API is running',
        'version': '1.0.0',
        'endpoints': {
            'health': '/health',
            'debug': '/debug',
            'test': '/test',
            'jobs': '/jobs',
            'tags': '/tags',
            'statistics': '/statistics'
        }
    })

# For Vercel, we need to export the app directly
# Vercel will handle the WSGI interface automatically
