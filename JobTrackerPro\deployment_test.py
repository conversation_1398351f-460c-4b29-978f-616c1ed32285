#!/usr/bin/env python3
"""
Final deployment test to verify everything works for Vercel
"""
import sys
import os
import requests
import json

# Add the api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

def test_vercel_structure():
    """Test if the Vercel deployment structure is correct"""
    print("🧪 Testing Vercel deployment structure...")
    
    required_files = [
        'api/index.py',
        'api/app.py', 
        'api/models.py',
        'api/database.py',
        'vercel.json',
        'requirements.txt',
        'index.html',
        'static/app.js',
        'static/utils/api.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    else:
        print("✅ All required files present")
        return True

def test_api_import():
    """Test if the API can be imported correctly"""
    print("\n🧪 Testing API imports for Vercel...")
    
    try:
        from api.index import app
        print("✅ Successfully imported Vercel app")
        
        # Test if we can get routes
        with app.app_context():
            routes = []
            for rule in app.url_map.iter_rules():
                if not rule.rule.startswith('/static'):
                    routes.append(f"{list(rule.methods)} {rule.rule}")
            
            print(f"📋 Available API routes ({len(routes)}):")
            for route in sorted(routes):
                print(f"   {route}")
                
        return True
    except Exception as e:
        print(f"❌ API import failed: {e}")
        return False

def test_job_creation_payload():
    """Test the job creation payload structure"""
    print("\n🧪 Testing job creation payload...")
    
    test_payload = {
        "company": "Test Company",
        "position": "Software Engineer",
        "location": "Remote",
        "status": "Applied",
        "application_date": "2025-05-31",
        "notes": "Test job application"
    }
    
    try:
        # Validate payload structure
        required_fields = ["company", "position"]
        for field in required_fields:
            if field not in test_payload:
                raise ValueError(f"Missing required field: {field}")
        
        print("✅ Job creation payload structure is valid")
        print(f"📄 Test payload: {json.dumps(test_payload, indent=2)}")
        return True
    except Exception as e:
        print(f"❌ Payload validation failed: {e}")
        return False

def main():
    """Run all deployment tests"""
    print("🚀 JobTrackerPro Vercel Deployment Test")
    print("=" * 50)
    
    # Test 1: File structure
    structure_ok = test_vercel_structure()
    
    # Test 2: API imports
    import_ok = test_api_import()
    
    # Test 3: Payload structure
    payload_ok = test_job_creation_payload()
    
    print("\n" + "=" * 50)
    if structure_ok and import_ok and payload_ok:
        print("✅ ALL TESTS PASSED! Ready for Vercel deployment.")
        print("\n🚀 Deployment Instructions:")
        print("1. Commit all changes to git")
        print("2. Push to your repository")
        print("3. Deploy to Vercel")
        print("4. Set up DATABASE_URL environment variable in Vercel")
        print("5. Test the deployed endpoints:")
        print("   - GET /api/health")
        print("   - GET /api/jobs")
        print("   - POST /api/jobs")
    else:
        print("❌ Some tests failed. Fix the issues above before deploying.")

if __name__ == "__main__":
    main()
