#!/usr/bin/env python3
"""
Database Migration Script for JobTrackerPro
This script creates all necessary tables in the Neon PostgreSQL database
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add current directory to path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import create_app
from database import db
import models

def run_migration():
    """Run database migration to create all tables"""
    print("🚀 Starting database migration...")
    
    # Create Flask app
    app = create_app()
    
    with app.app_context():
        try:
            # Drop all tables (optional - be careful in production!)
            print("📋 Checking existing tables...")
            
            # Create all tables
            print("🏗️  Creating database tables...")
            db.create_all()
            
            # Verify tables were created
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            
            print("✅ Migration completed successfully!")
            print(f"📊 Created tables: {', '.join(tables)}")
            
            # Print table details
            for table_name in tables:
                columns = inspector.get_columns(table_name)
                print(f"   📋 {table_name}: {len(columns)} columns")
                
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            return False
            
    return True

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("\n🎉 Database migration completed successfully!")
        print("💾 Your Neon database is ready to use.")
    else:
        print("\n💥 Migration failed. Please check the error above.")
        sys.exit(1)
