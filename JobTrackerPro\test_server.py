#!/usr/bin/env python3
"""
Test server to verify API endpoints work locally
"""
import sys
import os

# Add the api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

from api.index import create_app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Starting test server on http://localhost:5000")
    print("📋 Available endpoints:")
    print("   GET /api/health - Health check")
    print("   GET /api/debug - Debug info")
    print("   GET /api/jobs - List jobs")
    print("   POST /api/jobs - Create job")
    print("   GET /api/tags - List tags")
    print("   POST /api/tags - Create tag")
    print("\n🌐 Frontend: http://localhost:5000")
    print("🛑 Press Ctrl+C to stop")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
