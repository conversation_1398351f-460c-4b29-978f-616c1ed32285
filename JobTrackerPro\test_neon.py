#!/usr/bin/env python3
"""
Test NeonDB connection and migrate schema
"""
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

def test_neon_connection():
    """Test connection to NeonDB and create tables"""
    print("🧪 Testing NeonDB connection...")
    
    # Set environment to use NeonDB
    os.environ['DATABASE_URL'] = os.getenv('DATABASE_URL')
    
    try:
        from api.index import create_app
        from api.database import db
        
        app = create_app()
        
        with app.app_context():
            # Test connection
            print("🔗 Testing database connection...")
            result = db.session.execute(db.text('SELECT 1')).scalar()
            print(f"✅ Successfully connected to NeonDB! Test query result: {result}")

            # Test table creation
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()

            print(f"📊 Existing tables: {tables}")

            # Test inserting a sample job
            print("🧪 Testing job creation...")
            from api.models import JobApplication

            # Check if we can create a job
            test_job = JobApplication(
                company="NeonDB Test Company",
                position="Test Position",
                status="Applied"
            )

            db.session.add(test_job)
            db.session.commit()

            # Verify it was created
            job_count = JobApplication.query.count()
            print(f"✅ Successfully created test job! Total jobs in NeonDB: {job_count}")

            return True
            
    except Exception as e:
        print(f"❌ NeonDB connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_neon_connection()
    if success:
        print("\n🎉 NeonDB setup complete! Ready for production deployment.")
    else:
        print("\n⚠️  NeonDB setup failed. Check your connection string.")
