modules = ["python-3.11"]

[nix]
channel = "stable-24_05"

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Flask Server"

[[workflows.workflow]]
name = "Flask Server"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "pip install flask flask-sqlalchemy flask-cors psycopg2-binary && python main.py"
waitForPort = 5000

[deployment]
run = ["sh", "-c", "pip install flask flask-sqlalchemy flask-cors psycopg2-binary && python main.py"]

[[ports]]
localPort = 5000
externalPort = 80
