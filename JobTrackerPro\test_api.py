#!/usr/bin/env python3
"""
Test script to verify API endpoints are working correctly
"""
import sys
import os
import requests
import json

# Add the api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

def test_local_import():
    """Test if we can import the modules locally"""
    print("🧪 Testing local imports...")
    
    try:
        # Test importing from api directory
        from api.database import db
        print("✅ Successfully imported database from api/")
        
        from api.models import JobApplication, Tag
        print("✅ Successfully imported models from api/")
        
        from api.app import api_bp
        print("✅ Successfully imported api_bp from api/")
        
        from api.index import create_app
        print("✅ Successfully imported create_app from api/")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_app_creation():
    """Test if we can create the Flask app"""
    print("\n🧪 Testing app creation...")
    
    try:
        from api.index import create_app
        app = create_app()
        print("✅ Successfully created Flask app")
        
        # Test if we can get the routes
        with app.app_context():
            routes = []
            for rule in app.url_map.iter_rules():
                routes.append(f"{rule.methods} {rule.rule}")
            
            print(f"📋 Available routes ({len(routes)}):")
            for route in sorted(routes):
                print(f"   {route}")
                
        return True
    except Exception as e:
        print(f"❌ App creation failed: {e}")
        return False

def test_api_endpoints(base_url="http://localhost:5000"):
    """Test API endpoints if server is running"""
    print(f"\n🧪 Testing API endpoints at {base_url}...")
    
    endpoints_to_test = [
        ("/api/health", "GET"),
        ("/api/debug", "GET"),
        ("/api/jobs", "GET"),
        ("/api/tags", "GET"),
    ]
    
    for endpoint, method in endpoints_to_test:
        try:
            url = f"{base_url}{endpoint}"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                print(f"✅ {method} {endpoint} - Status: {response.status_code}")
                try:
                    data = response.json()
                    print(f"   📄 Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    print(f"   📄 Response: {response.text[:100]}...")
            else:
                print(f"⚠️  {method} {endpoint} - Status: {response.status_code}")
                print(f"   📄 Response: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print(f"🔌 {method} {endpoint} - Server not running")
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def main():
    """Run all tests"""
    print("🚀 JobTrackerPro API Test Suite")
    print("=" * 50)
    
    # Test 1: Local imports
    import_success = test_local_import()
    
    # Test 2: App creation
    app_success = test_app_creation()
    
    # Test 3: API endpoints (if server is running)
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    if import_success and app_success:
        print("✅ All local tests passed! Ready for deployment.")
    else:
        print("❌ Some tests failed. Check the errors above.")
        
    print("\n💡 To test with a running server:")
    print("   1. Run: python main.py")
    print("   2. Run: python test_api.py")

if __name__ == "__main__":
    main()
