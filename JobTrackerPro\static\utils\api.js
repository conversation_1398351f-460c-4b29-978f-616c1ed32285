// API utility for making HTTP requests
const api = {
  baseURL: '/api',

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    // Add debugging for production
    console.log(`🔄 API Request: ${config.method || 'GET'} ${url}`);
    if (config.body) {
      console.log('📤 Request body:', config.body);
    }

    try {
      const response = await fetch(url, config);

      console.log(`📡 Response status: ${response.status} for ${url}`);

      if (!response.ok) {
        // Try to get error details
        let errorData = {};
        try {
          const text = await response.text();
          console.log('❌ Error response text:', text);
          try {
            errorData = JSON.parse(text);
          } catch {
            errorData = { error: text || `HTTP error! status: ${response.status}` };
          }
        } catch {
          errorData = { error: `HTTP error! status: ${response.status}` };
        }

        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }

      // Handle different content types
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await response.json();
        console.log('✅ Response data:', data);
        return { data };
      } else {
        const data = await response.blob();
        console.log('✅ Response blob size:', data.size);
        return { data };
      }
    } catch (error) {
      console.error('❌ API request failed:', error);
      console.error('🔍 Request details:', { url, config });
      throw error;
    }
  },

  get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  },

  post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: data,
      ...options
    });
  },

  put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: data,
      ...options
    });
  },

  delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options });
  }
};
