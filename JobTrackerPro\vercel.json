{"version": 2, "builds": [{"src": "api/index.py", "use": "@vercel/python"}, {"src": "static/**", "use": "@vercel/static"}, {"src": "index.html", "use": "@vercel/static"}], "routes": [{"src": "/api/(.*)", "dest": "/api/index.py"}, {"src": "/static/(.*)", "dest": "/static/$1"}, {"src": "/", "dest": "/index.html"}, {"src": "/(.*\\.(js|css|html|ico|png|jpg|jpeg|gif|svg))", "dest": "/static/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"PYTHONPATH": "./api:.", "FLASK_ENV": "production"}}