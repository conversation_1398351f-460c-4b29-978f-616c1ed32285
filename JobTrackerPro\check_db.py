#!/usr/bin/env python3
"""
Check database schema and data
"""
import sqlite3
import os

def check_database():
    db_path = 'job_tracker.db'
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found")
        return
    
    print(f"✅ Database file found: {db_path}")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print(f"📊 Tables found: {len(tables)}")
    for table in tables:
        table_name = table[0]
        print(f"  - {table_name}")
        
        # Get table info
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        print(f"    Columns: {len(columns)}")
        for col in columns:
            print(f"      {col[1]} ({col[2]})")
        
        # Get row count
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"    Rows: {count}")
        print()
    
    conn.close()

if __name__ == "__main__":
    check_database()
