#!/usr/bin/env python3
"""
Test production deployment on Vercel
"""
import requests
import json
import time

PRODUCTION_URL = "https://job-tracker-gubukwx8c-suhailult777s-projects.vercel.app"

def test_production_api():
    """Test the production API endpoints"""
    print(f"🧪 Testing production deployment at: {PRODUCTION_URL}")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get(f"{PRODUCTION_URL}/api/health", timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Health check passed")
            print(f"   Database status: {data.get('database', 'unknown')}")
        else:
            print(f"   ⚠️ Health check failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
    
    # Test 2: Jobs endpoint
    print("\n2. Testing jobs endpoint...")
    try:
        response = requests.get(f"{PRODUCTION_URL}/api/jobs", timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            jobs = response.json()
            print(f"   ✅ Jobs endpoint working - found {len(jobs)} jobs")
        else:
            print(f"   ⚠️ Jobs endpoint failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Jobs endpoint error: {e}")
    
    # Test 3: Create a new job
    print("\n3. Testing job creation...")
    try:
        job_data = {
            "company": "Production Test Company",
            "position": "Test Engineer",
            "location": "Remote",
            "status": "Applied",
            "notes": "Created via production test"
        }
        
        response = requests.post(
            f"{PRODUCTION_URL}/api/jobs", 
            json=job_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            job = response.json()
            print(f"   ✅ Job created successfully - ID: {job.get('id')}")
            print(f"   Company: {job.get('company')}")
        else:
            print(f"   ⚠️ Job creation failed: {response.text}")
    except Exception as e:
        print(f"   ❌ Job creation error: {e}")
    
    # Test 4: Frontend
    print("\n4. Testing frontend...")
    try:
        response = requests.get(PRODUCTION_URL, timeout=30)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ Frontend accessible")
            if "JobTrackerPro" in response.text:
                print(f"   ✅ Frontend content looks correct")
            else:
                print(f"   ⚠️ Frontend content might be incorrect")
        else:
            print(f"   ⚠️ Frontend failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Frontend error: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Production testing complete!")
    print(f"🌐 Visit your app: {PRODUCTION_URL}")

if __name__ == "__main__":
    test_production_api()
