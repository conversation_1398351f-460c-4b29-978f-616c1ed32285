const FilterBar = ({ tags, onCreateTag, loading }) => {
  const { filters, setFilters } = useAppContext();
  const [showTagForm, setShowTagForm] = useState(false);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#2563EB');

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'Applied', label: 'Applied' },
    { value: 'Interviewing', label: 'Interviewing' },
    { value: 'Offer', label: 'Offer' },
    { value: 'Rejected', label: 'Rejected' },
    { value: 'Withdrawn', label: 'Withdrawn' }
  ];

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleTagToggle = (tagId) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tagId)
        ? prev.tags.filter(id => id !== tagId)
        : [...prev.tags, tagId]
    }));
  };

  const handleCreateTag = async (e) => {
    e.preventDefault();
    if (!newTagName.trim()) return;

    try {
      await onCreateTag({ name: newTagName.trim(), color: newTagColor });
      setNewTagName('');
      setNewTagColor('#2563EB');
      setShowTagForm(false);
    } catch (err) {
      console.error('Failed to create tag:', err);
    }
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      search: '',
      tags: [],
      company: '',
      dateFrom: '',
      dateTo: ''
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    Array.isArray(value) ? value.length > 0 : value !== ''
  );

  return React.createElement('div', { className: 'bg-white rounded-lg shadow-sm border p-6 mb-6' },
    React.createElement('div', { className: 'flex flex-wrap items-center gap-4 mb-4' },
      // Search
      React.createElement('div', { className: 'flex-1 min-w-64' },
        React.createElement('input', {
          type: 'text',
          placeholder: 'Search jobs...',
          value: filters.search,
          onChange: (e) => handleFilterChange('search', e.target.value),
          className: 'form-input'
        })
      ),

      // Status filter
      React.createElement('div', { className: 'min-w-40' },
        React.createElement('select', {
          value: filters.status,
          onChange: (e) => handleFilterChange('status', e.target.value),
          className: 'form-select'
        },
          statusOptions.map(option =>
            React.createElement('option', { key: option.value, value: option.value }, option.label)
          )
        )
      ),

      // Company filter
      React.createElement('div', { className: 'min-w-40' },
        React.createElement('input', {
          type: 'text',
          placeholder: 'Company...',
          value: filters.company,
          onChange: (e) => handleFilterChange('company', e.target.value),
          className: 'form-input'
        })
      ),

      // Date filters
      React.createElement('div', { className: 'flex gap-2' },
        React.createElement('input', {
          type: 'date',
          value: filters.dateFrom,
          onChange: (e) => handleFilterChange('dateFrom', e.target.value),
          className: 'form-input'
        }),
        React.createElement('input', {
          type: 'date',
          value: filters.dateTo,
          onChange: (e) => handleFilterChange('dateTo', e.target.value),
          className: 'form-input'
        })
      ),

      // Clear filters
      hasActiveFilters && React.createElement('button', {
        onClick: clearFilters,
        className: 'px-3 py-2 text-sm text-gray-600 hover:text-gray-800'
      }, 'Clear Filters')
    ),

    // Tags section
    React.createElement('div', null,
      React.createElement('div', { className: 'flex items-center gap-2 mb-3' },
        React.createElement('span', { className: 'text-sm font-medium text-gray-700' }, 'Filter by tags:'),
        React.createElement('button', {
          onClick: () => setShowTagForm(!showTagForm),
          className: 'text-sm text-blue-600 hover:text-blue-800'
        }, '+ Add Tag')
      ),

      // Tag creation form
      showTagForm && React.createElement('form', {
        onSubmit: handleCreateTag,
        className: 'flex items-center gap-2 mb-3 p-3 bg-gray-50 rounded'
      },
        React.createElement('input', {
          type: 'text',
          placeholder: 'Tag name',
          value: newTagName,
          onChange: (e) => setNewTagName(e.target.value),
          className: 'form-input flex-1',
          required: true
        }),
        React.createElement('input', {
          type: 'color',
          value: newTagColor,
          onChange: (e) => setNewTagColor(e.target.value),
          className: 'w-10 h-10 rounded border border-gray-300'
        }),
        React.createElement('button', {
          type: 'submit',
          disabled: loading,
          className: 'btn-primary text-sm disabled:opacity-50'
        }, 'Add'),
        React.createElement('button', {
          type: 'button',
          onClick: () => setShowTagForm(false),
          className: 'px-3 py-2 text-sm text-gray-600 hover:text-gray-800'
        }, 'Cancel')
      ),

      // Tags list
      React.createElement('div', { className: 'flex flex-wrap gap-2' },
        tags.map(tag =>
          React.createElement('button', {
            key: tag.id,
            onClick: () => handleTagToggle(tag.id),
            className: `tag ${
              filters.tags.includes(tag.id) 
                ? 'ring-2 ring-blue-500' 
                : 'hover:opacity-80'
            }`,
            style: { 
              backgroundColor: `${tag.color}20`, 
              color: tag.color,
              border: `1px solid ${tag.color}40`
            }
          }, tag.name)
        ),
        tags.length === 0 && React.createElement('span', { 
          className: 'text-sm text-gray-500' 
        }, 'No tags created yet')
      )
    )
  );
};
