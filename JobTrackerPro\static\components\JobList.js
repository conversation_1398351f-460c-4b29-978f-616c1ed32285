const JobList = ({ jobs, onEdit, onDelete, loading }) => {
  const { view } = useAppContext();

  const getStatusColor = (status) => {
    const colors = {
      'Applied': 'bg-blue-100 text-blue-800',
      'Interviewing': 'bg-yellow-100 text-yellow-800', 
      'Offer': 'bg-green-100 text-green-800',
      'Rejected': 'bg-red-100 text-red-800',
      'Withdrawn': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return React.createElement('div', { className: 'flex justify-center py-8' },
      React.createElement('div', { className: 'text-gray-500' }, 'Loading...')
    );
  }

  if (jobs.length === 0) {
    return React.createElement('div', { className: 'text-center py-12' },
      React.createElement('div', { className: 'text-gray-500' },
        React.createElement('p', { className: 'text-lg' }, 'No job applications found'),
        React.createElement('p', { className: 'mt-2' }, 'Create your first application to get started')
      )
    );
  }

  if (view === 'cards') {
    return React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' },
      jobs.map(job =>
        React.createElement(JobCard, {
          key: job.id,
          job,
          onEdit,
          onDelete
        })
      )
    );
  }

  // Table view
  return React.createElement('div', { className: 'bg-white rounded-lg shadow-sm border overflow-hidden' },
    React.createElement('div', { className: 'overflow-x-auto' },
      React.createElement('table', { className: 'min-w-full divide-y divide-gray-200' },
        React.createElement('thead', { className: 'bg-gray-50' },
          React.createElement('tr', null,
            React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, 
              'Company & Position'
            ),
            React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, 
              'Status'
            ),
            React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, 
              'Applied'
            ),
            React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, 
              'Tags'
            ),
            React.createElement('th', { className: 'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider' }, 
              'Salary'
            ),
            React.createElement('th', { className: 'relative px-6 py-3' },
              React.createElement('span', { className: 'sr-only' }, 'Actions')
            )
          )
        ),
        React.createElement('tbody', { className: 'bg-white divide-y divide-gray-200' },
          jobs.map(job =>
            React.createElement('tr', { 
              key: job.id, 
              className: 'table-row hover:bg-gray-50 transition-colors' 
            },
              React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap' },
                React.createElement('div', null,
                  React.createElement('div', { className: 'text-sm font-medium text-gray-900' }, 
                    job.position
                  ),
                  React.createElement('div', { className: 'text-sm text-gray-500' }, 
                    job.company
                  ),
                  job.location && React.createElement('div', { className: 'text-xs text-gray-400' }, 
                    job.location
                  )
                )
              ),
              React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap' },
                React.createElement('span', { 
                  className: `status-badge ${getStatusColor(job.status)}` 
                }, job.status)
              ),
              React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-500' },
                formatDate(job.application_date)
              ),
              React.createElement('td', { className: 'px-6 py-4' },
                React.createElement('div', { className: 'flex flex-wrap gap-1' },
                  job.tags && job.tags.slice(0, 2).map(tag =>
                    React.createElement('span', {
                      key: tag.id,
                      className: 'tag',
                      style: {
                        backgroundColor: `${tag.color}20`,
                        color: tag.color,
                        border: `1px solid ${tag.color}40`
                      }
                    }, tag.name)
                  ),
                  job.tags && job.tags.length > 2 && React.createElement('span', {
                    className: 'text-xs text-gray-500'
                  }, `+${job.tags.length - 2} more`)
                )
              ),
              React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-sm text-gray-500' },
                job.salary_range || '-'
              ),
              React.createElement('td', { className: 'px-6 py-4 whitespace-nowrap text-right text-sm font-medium' },
                React.createElement('div', { className: 'flex justify-end space-x-2' },
                  job.job_url && React.createElement('a', {
                    href: job.job_url,
                    target: '_blank',
                    rel: 'noopener noreferrer',
                    className: 'text-blue-600 hover:text-blue-900'
                  }, 'View'),
                  React.createElement('button', {
                    onClick: () => onEdit(job),
                    className: 'text-indigo-600 hover:text-indigo-900'
                  }, 'Edit'),
                  React.createElement('button', {
                    onClick: () => onDelete(job.id),
                    className: 'text-red-600 hover:text-red-900'
                  }, 'Delete')
                )
              )
            )
          )
        )
      )
    )
  );
};
