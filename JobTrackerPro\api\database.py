import os
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.orm import DeclarativeBase

class Base(DeclarativeBase):
    pass

db = SQLAlchemy(model_class=Base)

def get_database_url():
    """Get the database URL from environment variables or use SQLite as fallback"""
    # Check for PostgreSQL URL first (for production)
    postgres_url = os.getenv('POSTGRES_URL')
    if postgres_url:
        # Fix postgres:// to postgresql:// for SQLAlchemy 1.4+
        if postgres_url.startswith('postgres://'):
            postgres_url = postgres_url.replace('postgres://', 'postgresql://', 1)
        return postgres_url

    # Check for other database URLs
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        # Fix postgres:// to postgresql:// for SQLAlchemy 1.4+
        if database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://', 1)
        return database_url

    # For Vercel serverless, use in-memory SQLite
    if os.getenv('VERCEL'):
        return 'sqlite:///:memory:'

    # Fallback to SQLite for local development
    return 'sqlite:///job_tracker.db'