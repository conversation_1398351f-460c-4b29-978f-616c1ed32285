@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Custom Colors - HSL format for proper CSS variable usage */
  --primary: 213 94% 68%; /* #2563EB */
  --secondary: 158 64% 52%; /* #10B981 */
  --warning: 43 96% 56%; /* #F59E0B */
  --danger: 0 84% 60%; /* #EF4444 */
  --background: 210 40% 98%; /* #F8FAFC */
  --foreground: 215 25% 27%; /* #1E293B */
  --muted: 210 40% 96%; /* #F1F5F9 */
  --border: 214 32% 91%; /* #E2E8F0 */
  --input: 214 32% 91%; /* #E2E8F0 */
  --card: 0 0% 100%; /* #FFFFFF */
  --card-foreground: 215 25% 27%; /* #1E293B */
  --popover: 0 0% 100%; /* #FFFFFF */
  --popover-foreground: 215 25% 27%; /* #1E293B */
  --primary-foreground: 0 0% 100%; /* #FFFFFF */
  --secondary-foreground: 0 0% 100%; /* #FFFFFF */
  --muted-foreground: 215 16% 47%; /* #64748B */
  --accent: 210 40% 94%; /* #F1F5F9 */
  --accent-foreground: 215 25% 27%; /* #1E293B */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --ring: 213 94% 68%; /* #2563EB */
  --radius: 0.5rem;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  line-height: 1.5;
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-applied {
  @apply bg-blue-100 text-blue-800;
}

.status-interviewing {
  @apply bg-yellow-100 text-yellow-800;
}

.status-offer {
  @apply bg-green-100 text-green-800;
}

.status-rejected {
  @apply bg-red-100 text-red-800;
}

.status-withdrawn {
  @apply bg-gray-100 text-gray-800;
}

.btn-primary {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  @apply px-4 py-2 rounded-md font-medium hover:opacity-90 transition-opacity;
}

.btn-secondary {
  background-color: hsl(var(--secondary));
  color: hsl(var(--secondary-foreground));
  @apply px-4 py-2 rounded-md font-medium hover:opacity-90 transition-opacity;
}

.btn-danger {
  background-color: hsl(var(--danger));
  color: hsl(var(--destructive-foreground));
  @apply px-4 py-2 rounded-md font-medium hover:opacity-90 transition-opacity;
}

.card {
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border: 1px solid hsl(var(--border));
  @apply rounded-lg shadow-sm;
}

.input {
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--input));
  color: hsl(var(--foreground));
  @apply px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.tag {
  @apply inline-flex items-center px-2 py-1 rounded-md text-xs font-medium;
}

.loading {
  @apply animate-pulse;
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.table-row:hover {
  background-color: hsl(var(--muted));
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical;
}

.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white;
}
