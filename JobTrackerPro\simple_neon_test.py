#!/usr/bin/env python3
"""
Simple NeonDB connection test using psycopg2
"""
import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_neon_simple():
    """Simple test of NeonDB connection"""
    print("🧪 Testing NeonDB connection with psycopg2...")
    
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        print("❌ No DATABASE_URL found in environment")
        return False
    
    print(f"🔗 Connecting to: {database_url[:50]}...")
    
    try:
        # Connect to the database
        conn = psycopg2.connect(database_url)
        cursor = conn.cursor()
        
        # Test basic query
        cursor.execute('SELECT version()')
        version = cursor.fetchone()
        print(f"✅ Connected successfully! PostgreSQL version: {version[0][:50]}...")
        
        # Check if our tables exist
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = cursor.fetchall()
        
        if tables:
            print(f"📊 Existing tables: {[t[0] for t in tables]}")
        else:
            print("📊 No tables found - database is empty")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_neon_simple()
    if success:
        print("\n🎉 NeonDB connection successful!")
        print("💡 The database is ready for production deployment.")
    else:
        print("\n⚠️  NeonDB connection failed.")
        print("💡 Check your DATABASE_URL in the .env file.")
